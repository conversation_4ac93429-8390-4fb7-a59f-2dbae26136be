# Changelog

All notable changes to the Luminari Wilderness Editor will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial project setup with React, TypeScript, and Vite
- Basic project structure and configuration files
- Comprehensive documentation suite
- Supabase authentication integration with OAuth support
- Core wilderness editor UI components (MapCanvas, ToolPalette, LayerControls, PropertiesPanel)
- Drawing tools for points, polygons, and linestrings
- Real-time coordinate display with zoom-adjusted precision
- Layer visibility controls for grid, regions, and paths
- Keyboard shortcuts for tool selection (S, P, G, L, Escape, Enter)
- Mock data system for development
- Custom React hooks for editor state management (useEditor)
- Authentication flow with protected routes
- Status bar with coordinates and zoom controls
- Environment configuration template (.env.example)
- Netlify deployment configuration with SPA routing
- CLAUDE.md file for AI assistant guidance
- TypeScript type definitions for all wilderness entities

### Changed
- Updated project metadata in package.json (name, description, author, repository)
- Configured project for LuminariMUD wilderness system integration
- Set up coordinate system matching MUD wilderness (-1024 to +1024)

### Deprecated
- N/A

### Removed
- N/A

### Fixed
- N/A

### Security
- Implemented protected routes requiring authentication
- Added environment variable configuration for API keys
- Configured Supabase authentication with secure OAuth flow

## [0.1.0] - 2024-01-XX

### Added
- Project initialization
- Basic React application structure
- TypeScript configuration
- Tailwind CSS setup
- ESLint configuration
- Vite build system
- Initial documentation structure

---

## Release Notes Template

When creating a new release, use this template:

```markdown
## [X.Y.Z] - YYYY-MM-DD

### Added
- New features and functionality

### Changed
- Changes to existing functionality

### Deprecated
- Features that will be removed in future versions

### Removed
- Features that have been removed

### Fixed
- Bug fixes

### Security
- Security improvements and vulnerability fixes
```

## Version Numbering

This project follows [Semantic Versioning](https://semver.org/):

- **MAJOR** version when you make incompatible API changes
- **MINOR** version when you add functionality in a backwards compatible manner
- **PATCH** version when you make backwards compatible bug fixes

## Categories

### Added
For new features.

### Changed
For changes in existing functionality.

### Deprecated
For soon-to-be removed features.

### Removed
For now removed features.

### Fixed
For any bug fixes.

### Security
In case of vulnerabilities.
