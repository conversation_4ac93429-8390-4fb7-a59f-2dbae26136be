# Luminari Wilderness Editor - Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# API Configuration
# =============================================================================

# Backend API URL
VITE_API_URL=http://localhost:8000/api

# WebSocket URL for real-time features
VITE_WS_URL=ws://localhost:8000/ws

# =============================================================================
# Authentication (Supabase)
# =============================================================================

# Supabase project URL
VITE_SUPABASE_URL=your_supabase_project_url

# Supabase anonymous key (public key)
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Production Domain Configuration
# Make sure to configure this domain in your Supabase project settings:
# - Site URL: https://wildedit.luminarimud.com
# - Redirect URLs: https://wildedit.luminarimud.com/auth/callback

# =============================================================================
# Map Configuration
# =============================================================================

# Base URL for map images and tiles
VITE_MAP_BASE_URL=http://localhost:8000/maps

# Default zoom level (1 = 100%)
VITE_DEFAULT_ZOOM=1

# Maximum zoom level
VITE_MAX_ZOOM=4

# Minimum zoom level
VITE_MIN_ZOOM=0.5

# Map image format (png, jpg, webp)
VITE_MAP_FORMAT=png

# =============================================================================
# Feature Flags
# =============================================================================

# Enable debug mode (shows additional logging and debug info)
VITE_ENABLE_DEBUG=true

# Enable analytics tracking
VITE_ENABLE_ANALYTICS=false

# Enable experimental features
VITE_ENABLE_EXPERIMENTAL=false

# Enable offline mode support
VITE_ENABLE_OFFLINE=false

# =============================================================================
# Application Settings
# =============================================================================

# Application name
VITE_APP_NAME=Luminari Wilderness Editor

# Application version (automatically set by build process)
VITE_APP_VERSION=0.1.0

# Environment (development, staging, production)
VITE_ENVIRONMENT=development

# =============================================================================
# Performance Settings
# =============================================================================

# Maximum number of features to render at once
VITE_MAX_FEATURES=1000

# Debounce delay for search and filtering (ms)
VITE_DEBOUNCE_DELAY=300

# Auto-save interval (ms)
VITE_AUTOSAVE_INTERVAL=30000

# =============================================================================
# UI Configuration
# =============================================================================

# Default theme (light, dark, auto)
VITE_DEFAULT_THEME=light

# Enable animations
VITE_ENABLE_ANIMATIONS=true

# Default language
VITE_DEFAULT_LANGUAGE=en

# =============================================================================
# Development Settings
# =============================================================================

# Enable hot module replacement
VITE_HMR=true

# Enable source maps in development
VITE_SOURCE_MAPS=true

# Mock API responses (for development without backend)
VITE_MOCK_API=false

# =============================================================================
# Error Tracking
# =============================================================================

# Sentry DSN for error tracking (optional)
VITE_SENTRY_DSN=

# Error reporting environment
VITE_SENTRY_ENVIRONMENT=development

# =============================================================================
# External Services
# =============================================================================

# Google Analytics tracking ID (optional)
VITE_GA_TRACKING_ID=

# Google Maps API key (if using Google Maps features)
VITE_GOOGLE_MAPS_API_KEY=

# =============================================================================
# Security Settings
# =============================================================================

# Content Security Policy nonce (generated at build time)
VITE_CSP_NONCE=

# Allowed origins for CORS (comma-separated)
VITE_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# =============================================================================
# Notes
# =============================================================================

# Environment Variable Naming:
# - All frontend environment variables must start with VITE_
# - Use SCREAMING_SNAKE_CASE for variable names
# - Boolean values should be 'true' or 'false' (strings)
# - URLs should not have trailing slashes

# Security Notes:
# - Never commit actual API keys or secrets to version control
# - Use different values for development, staging, and production
# - The VITE_SUPABASE_ANON_KEY is safe to expose (it's public)
# - Keep your Supabase service role key secret (backend only)

# Development vs Production:
# - Development: Use localhost URLs and enable debug features
# - Production: Use HTTPS URLs and disable debug features
# - Staging: Use staging URLs with production-like settings
