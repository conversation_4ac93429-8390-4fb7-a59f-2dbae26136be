## Description

Brief description of the changes made in this pull request.

## Type of Change

Please delete options that are not relevant.

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Code refactoring (no functional changes)
- [ ] Performance improvement
- [ ] Test addition or update

## Related Issues

Closes #(issue_number)
Fixes #(issue_number)
Relates to #(issue_number)

## Changes Made

### Frontend Changes
- [ ] Added new components
- [ ] Modified existing components
- [ ] Updated styles/CSS
- [ ] Added new hooks
- [ ] Updated types/interfaces
- [ ] Other: _describe_

### Backend Changes (if applicable)
- [ ] Added new API endpoints
- [ ] Modified existing endpoints
- [ ] Database schema changes
- [ ] Added new services/utilities
- [ ] Updated authentication/authorization
- [ ] Other: _describe_

## Testing

### Manual Testing
- [ ] Tested on desktop browsers (Chrome, Firefox, Safari, Edge)
- [ ] Tested on mobile devices
- [ ] Tested with different zoom levels
- [ ] Tested with different screen sizes
- [ ] Tested edge cases and error scenarios

### Automated Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] E2E tests added/updated
- [ ] All existing tests pass
- [ ] Code coverage maintained/improved

## Screenshots/Videos

If applicable, add screenshots or videos demonstrating the changes.

### Before
<!-- Add screenshots of the current state -->

### After
<!-- Add screenshots of the new state -->

## Checklist

### Code Quality
- [ ] Code follows the project's style guidelines
- [ ] Self-review of code completed
- [ ] Code is properly commented
- [ ] No console.log statements left in production code
- [ ] No TODO comments left unaddressed

### Documentation
- [ ] Documentation updated (if needed)
- [ ] API documentation updated (if applicable)
- [ ] README updated (if needed)
- [ ] CHANGELOG updated

### Security
- [ ] No sensitive information exposed
- [ ] Input validation implemented where needed
- [ ] Authentication/authorization properly handled
- [ ] CORS settings appropriate

### Performance
- [ ] No performance regressions introduced
- [ ] Large files/assets optimized
- [ ] Database queries optimized (if applicable)
- [ ] Memory leaks checked

### Accessibility
- [ ] Keyboard navigation works
- [ ] Screen reader compatibility maintained
- [ ] Color contrast meets standards
- [ ] Alt text provided for images

## Deployment Notes

Any special deployment considerations:

- [ ] Database migrations required
- [ ] Environment variables need updating
- [ ] Third-party service configuration needed
- [ ] Cache clearing required
- [ ] Other: _describe_

## Breaking Changes

If this is a breaking change, describe:

1. What breaks
2. How to migrate existing code/data
3. Timeline for deprecation (if applicable)

## Additional Notes

Any additional information that reviewers should know:

- Performance implications
- Known limitations
- Future improvements planned
- Dependencies added/removed

## Review Checklist for Maintainers

- [ ] Code review completed
- [ ] Tests reviewed and passing
- [ ] Documentation reviewed
- [ ] Security implications considered
- [ ] Performance impact assessed
- [ ] Breaking changes documented
- [ ] Deployment plan reviewed
